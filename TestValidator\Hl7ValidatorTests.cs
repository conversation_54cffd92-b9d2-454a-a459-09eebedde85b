﻿using Hl7Validator;

namespace TestValidator;

[TestClass]
public sealed class Hl7ValidatorTests
{
    [TestMethod]
    public void ValidationConfig_DefaultValues_AreCorrect()
    {
        // Arrange & Act
        var config = new ValidationConfig();

        // Assert
        Assert.IsNull(config.MessageLimit);
    }

    [TestMethod]
    public void ValidationResult_DefaultValues_AreCorrect()
    {
        // Arrange & Act
        var result = new ValidationResult();

        // Assert
        Assert.AreEqual(string.Empty, result.FilePath);
        Assert.IsFalse(result.IsValid);
        Assert.AreEqual(string.Empty, result.ErrorMessage);
        Assert.AreEqual(string.Empty, result.Version);
        Assert.AreEqual(0L, result.FileSizeBytes);
        Assert.AreEqual(TimeSpan.Zero, result.ProcessingTime);
    }

    [TestMethod]
    public void ValidationConfig_CanSetMessageLimit()
    {
        // Arrange
        var config = new ValidationConfig();
        var testLimit = 50;

        // Act
        config.MessageLimit = testLimit;

        // Assert
        Assert.AreEqual(testLimit, config.MessageLimit);
    }

    [TestMethod]
    public void ValidationResult_CanSetProperties()
    {
        // Arrange
        var result = new ValidationResult();
        var testPath = "test.hl7";
        var testVersion = "2.8";
        var testError = "Test error";
        var testSize = 1024L;
        var testTime = TimeSpan.FromMilliseconds(100);

        // Act
        result.FilePath = testPath;
        result.IsValid = true;
        result.ErrorMessage = testError;
        result.Version = testVersion;
        result.FileSizeBytes = testSize;
        result.ProcessingTime = testTime;

        // Assert
        Assert.AreEqual(testPath, result.FilePath);
        Assert.IsTrue(result.IsValid);
        Assert.AreEqual(testError, result.ErrorMessage);
        Assert.AreEqual(testVersion, result.Version);
        Assert.AreEqual(testSize, result.FileSizeBytes);
        Assert.AreEqual(testTime, result.ProcessingTime);
    }

    [TestMethod]
    public void Program_Type_Exists()
    {
        // Arrange & Act
        var programType = typeof(Program);

        // Assert
        Assert.IsNotNull(programType);
        Assert.AreEqual("Program", programType.Name);
    }

    [TestMethod]
    [DataRow(1)]
    [DataRow(10)]
    [DataRow(100)]
    public void ValidationConfig_MessageLimit_AcceptsValidValues(int limit)
    {
        // Arrange
        var config = new ValidationConfig();

        // Act
        config.MessageLimit = limit;

        // Assert
        Assert.AreEqual(limit, config.MessageLimit);
    }

    [TestMethod]
    public void BasicMathTest_Addition_WorksCorrectly()
    {
        // Arrange
        int a = 2;
        int b = 3;

        // Act
        int result = a + b;

        // Assert
        Assert.AreEqual(5, result);
    }
}