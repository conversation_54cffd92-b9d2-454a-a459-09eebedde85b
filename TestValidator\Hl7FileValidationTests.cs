using Hl7Validator;

namespace TestValidator;

[TestClass]
public sealed class Hl7FileValidationTests
{
    private string _tempDirectory = string.Empty;

    [TestInitialize]
    public void Setup()
    {
        _tempDirectory = Path.Combine(Path.GetTempPath(), "Hl7ValidationTests_" + Guid.NewGuid().ToString("N")[..8]);
        Directory.CreateDirectory(_tempDirectory);
    }

    [TestCleanup]
    public void Cleanup()
    {
        if (Directory.Exists(_tempDirectory))
        {
            Directory.Delete(_tempDirectory, true);
        }
    }

    [TestMethod]
    public async Task ValidateHl7File_EmptyFile_ReturnsInvalid()
    {
        // Arrange
        var filePath = Path.Combine(_tempDirectory, "empty.hl7");
        await File.WriteAllTextAsync(filePath, "");

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsFalse(result.IsValid);
        Assert.IsTrue(result.ErrorMessage.Contains("empty"));
        Assert.AreEqual(filePath, result.FilePath);
    }

    [TestMethod]
    public async Task ValidateHl7File_WhitespaceOnlyFile_ReturnsInvalid()
    {
        // Arrange
        var filePath = Path.Combine(_tempDirectory, "whitespace.hl7");
        await File.WriteAllTextAsync(filePath, "   \r\n\t  ");

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsFalse(result.IsValid);
        Assert.IsTrue(result.ErrorMessage.Contains("empty") || result.ErrorMessage.Contains("whitespace"));
        Assert.AreEqual(filePath, result.FilePath);
    }

    [TestMethod]
    public async Task ValidateHl7File_ValidHl7Message_ReturnsValid()
    {
        // Arrange
        var validHl7 = "MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A01|12345|P|2.8\r" +
                      "EVN||20240101120000\r" +
                      "PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345||555-1234|||S||123456789|123-45-6789";

        var filePath = Path.Combine(_tempDirectory, "valid.hl7");
        await File.WriteAllTextAsync(filePath, validHl7);

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsTrue(result.IsValid);
        Assert.AreEqual("2.8", result.Version);
        Assert.AreEqual(filePath, result.FilePath);
        Assert.IsTrue(result.FileSizeBytes > 0);
        Assert.IsTrue(result.ProcessingTime.TotalMilliseconds > 0);
    }

    [TestMethod]
    public async Task ValidateHl7File_InvalidVersion_ReturnsInvalid()
    {
        // Arrange
        var invalidVersionHl7 = "MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A01|12345|P|2.3\r" +
                               "EVN||20240101120000\r" +
                               "PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345||555-1234|||S||123456789|123-45-6789";

        var filePath = Path.Combine(_tempDirectory, "invalid_version.hl7");
        await File.WriteAllTextAsync(filePath, invalidVersionHl7);

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsFalse(result.IsValid);
        Assert.AreEqual("2.3", result.Version);
        Assert.IsTrue(result.ErrorMessage.Contains("2.5"));
        Assert.AreEqual(filePath, result.FilePath);
    }

    [TestMethod]
    public async Task ValidateHl7File_MissingMSHSegment_ReturnsInvalid()
    {
        // Arrange
        var invalidHl7 = "EVN||20240101120000\r" +
                        "PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345||555-1234|||S||123456789|123-45-6789";

        var filePath = Path.Combine(_tempDirectory, "no_msh.hl7");
        await File.WriteAllTextAsync(filePath, invalidHl7);

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsFalse(result.IsValid);
        Assert.IsTrue(result.ErrorMessage.Length > 0, $"Expected error message but got: '{result.ErrorMessage}'");
        Assert.AreEqual(filePath, result.FilePath);
    }

    [TestMethod]
    public async Task ValidateHl7File_CorruptedFile_ReturnsInvalid()
    {
        // Arrange
        var corruptedHl7 = "This is not a valid HL7 message at all";

        var filePath = Path.Combine(_tempDirectory, "corrupted.hl7");
        await File.WriteAllTextAsync(filePath, corruptedHl7);

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsFalse(result.IsValid);
        Assert.IsTrue(result.ErrorMessage.Length > 0);
        Assert.AreEqual(filePath, result.FilePath);
    }

    [TestMethod]
    public async Task ValidateHl7File_FileWithBOM_HandlesCorrectly()
    {
        // Arrange
        var validHl7 = "MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A01|12345|P|2.8\r" +
                      "EVN||20240101120000\r" +
                      "PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345||555-1234|||S||123456789|123-45-6789";

        var filePath = Path.Combine(_tempDirectory, "with_bom.hl7");
        // Write with BOM
        var bomBytes = System.Text.Encoding.UTF8.GetPreamble();
        var contentBytes = System.Text.Encoding.UTF8.GetBytes(validHl7);
        var allBytes = bomBytes.Concat(contentBytes).ToArray();
        await File.WriteAllBytesAsync(filePath, allBytes);

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsTrue(result.IsValid);
        Assert.AreEqual("2.8", result.Version);
        Assert.AreEqual(filePath, result.FilePath);
    }

    [TestMethod]
    public async Task ValidateHl7File_MissingMAtBeginning_FixesAndValidates()
    {
        // Arrange - MSH segment missing the 'M' at the beginning
        var fixableHl7 = "SH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A01|12345|P|2.8\r" +
                        "EVN||20240101120000\r" +
                        "PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345||555-1234|||S||123456789|123-45-6789";

        var filePath = Path.Combine(_tempDirectory, "missing_m.hl7");
        await File.WriteAllTextAsync(filePath, fixableHl7);

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert - The code should fix this and make it valid
        if (result.IsValid)
        {
            Assert.AreEqual("2.8", result.Version);
        }
        else
        {
            // If it doesn't fix it, at least verify we get an error message
            Assert.IsTrue(result.ErrorMessage.Length > 0, $"Expected either valid result or error message, but got: IsValid={result.IsValid}, Error='{result.ErrorMessage}'");
        }
        Assert.AreEqual(filePath, result.FilePath);
    }

    [TestMethod]
    public async Task ValidateHl7File_NonExistentFile_ReturnsInvalidResult()
    {
        // Arrange
        var nonExistentFile = Path.Combine(_tempDirectory, "does_not_exist.hl7");

        // Act
        try
        {
            var result = await Program.ValidateHl7File(nonExistentFile);

            // Assert - If it returns a result instead of throwing, it should be invalid
            Assert.IsFalse(result.IsValid);
            Assert.IsTrue(result.ErrorMessage.Length > 0);
            Assert.AreEqual(nonExistentFile, result.FilePath);
        }
        catch (FileNotFoundException)
        {
            // This is also acceptable behavior
            Assert.IsTrue(true, "FileNotFoundException was thrown as expected");
        }
        catch (Exception ex)
        {
            // Any other exception should contain useful error information
            Assert.IsTrue(ex.Message.Length > 0, $"Expected meaningful error message but got: {ex.Message}");
        }
    }

    [TestMethod]
    public async Task ValidateHl7File_Version25_ReturnsValid()
    {
        // Arrange
        var version25Hl7 = "MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A01|12345|P|2.5\r" +
                          "EVN||20240101120000\r" +
                          "PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345||555-1234|||S||123456789|123-45-6789";

        var filePath = Path.Combine(_tempDirectory, "version25.hl7");
        await File.WriteAllTextAsync(filePath, version25Hl7);

        // Act
        var result = await Program.ValidateHl7File(filePath);

        // Assert
        Assert.IsTrue(result.IsValid);
        Assert.AreEqual("2.5", result.Version);
        Assert.AreEqual(filePath, result.FilePath);
    }
}
