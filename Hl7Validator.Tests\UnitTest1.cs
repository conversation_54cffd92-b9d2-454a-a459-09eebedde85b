﻿namespace Hl7Validator.Hl7Validator.Tests;

public class Hl7ValidatorTests
{
    [Fact]
    public void ValidationConfig_DefaultValues_AreCorrect()
    {
        // Arrange & Act
        var config = new ValidationConfig();

        // Assert
        Assert.Null(config.MessageLimit);
    }

    [Fact]
    public void ValidationResult_DefaultValues_AreCorrect()
    {
        // Arrange & Act
        var result = new ValidationResult();

        // Assert
        Assert.Equal(string.Empty, result.FilePath);
        Assert.False(result.IsValid);
        Assert.Equal(string.Empty, result.ErrorMessage);
        Assert.Equal(string.Empty, result.Version);
        Assert.Equal(0, result.FileSizeBytes);
        Assert.Equal(TimeSpan.Zero, result.ProcessingTime);
    }

    [Fact]
    public void ValidationConfig_CanSetMessageLimit()
    {
        // Arrange
        var config = new ValidationConfig();
        var testLimit = 50;

        // Act
        config.MessageLimit = testLimit;

        // Assert
        Assert.Equal(testLimit, config.MessageLimit);
    }

    [Fact]
    public void ValidationResult_CanSetProperties()
    {
        // Arrange
        var result = new ValidationResult();
        var testPath = "test.hl7";
        var testVersion = "2.8";
        var testError = "Test error";
        var testSize = 1024L;
        var testTime = TimeSpan.FromMilliseconds(100);

        // Act
        result.FilePath = testPath;
        result.IsValid = true;
        result.ErrorMessage = testError;
        result.Version = testVersion;
        result.FileSizeBytes = testSize;
        result.ProcessingTime = testTime;

        // Assert
        Assert.Equal(testPath, result.FilePath);
        Assert.True(result.IsValid);
        Assert.Equal(testError, result.ErrorMessage);
        Assert.Equal(testVersion, result.Version);
        Assert.Equal(testSize, result.FileSizeBytes);
        Assert.Equal(testTime, result.ProcessingTime);
    }

    [Fact]
    public void Program_HasMainMethod()
    {
        // Arrange
        var programType = typeof(Program);

        // Act
        var mainMethod = programType.GetMethod("Main", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.Public);

        // Assert
        Assert.NotNull(mainMethod);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(10)]
    [InlineData(100)]
    public void ValidationConfig_MessageLimit_AcceptsValidValues(int limit)
    {
        // Arrange
        var config = new ValidationConfig();

        // Act
        config.MessageLimit = limit;

        // Assert
        Assert.Equal(limit, config.MessageLimit);
    }

    [Fact]
    public void BasicMathTest_Addition_WorksCorrectly()
    {
        // Arrange
        int a = 2;
        int b = 3;

        // Act
        int result = a + b;

        // Assert
        Assert.Equal(5, result);
    }
}
