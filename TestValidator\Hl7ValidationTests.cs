using Hl7Validator;

namespace TestValidator;

[TestClass]
public sealed class Hl7ValidationTests
{
    [TestMethod]
    public void IsVersion28OrAbove_ValidVersions_ReturnsTrue()
    {
        // Arrange & Act & Assert
        Assert.IsTrue(Program.IsVersion28OrAbove("2.8"));
        Assert.IsTrue(Program.IsVersion28OrAbove("2.9"));
        Assert.IsTrue(Program.IsVersion28OrAbove("3.0"));
        Assert.IsTrue(Program.IsVersion28OrAbove("2.8.1"));
        Assert.IsTrue(Program.IsVersion28OrAbove("2.5"));
        Assert.IsTrue(Program.IsVersion28OrAbove("2.6"));
        Assert.IsTrue(Program.IsVersion28OrAbove("2.7"));
    }

    [TestMethod]
    public void IsVersion28OrAbove_InvalidVersions_ReturnsFalse()
    {
        // Arrange & Act & Assert
        Assert.IsFalse(Program.IsVersion28OrAbove("2.4"));
        Assert.IsFalse(Program.IsVersion28OrAbove("2.3"));
        Assert.IsFalse(Program.IsVersion28OrAbove("1.0"));
        Assert.IsFalse(Program.IsVersion28OrAbove(""));
        Assert.IsFalse(Program.IsVersion28OrAbove("Unknown"));
        Assert.IsFalse(Program.IsVersion28OrAbove("invalid"));
        Assert.IsFalse(Program.IsVersion28OrAbove("2"));
        Assert.IsFalse(Program.IsVersion28OrAbove("abc"));
    }

    [TestMethod]
    public void GetHl7Files_ValidDirectory_ReturnsHl7Files()
    {
        // Arrange
        var testDir = Path.Combine(Path.GetTempPath(), "Hl7Test_" + Guid.NewGuid().ToString("N")[..8]);
        Directory.CreateDirectory(testDir);

        try
        {
            // Create test files
            File.WriteAllText(Path.Combine(testDir, "test1.hl7"), "MSH|^~\\&|");
            File.WriteAllText(Path.Combine(testDir, "test2.txt"), "MSH|^~\\&|");
            File.WriteAllText(Path.Combine(testDir, "test3.msg"), "MSH|^~\\&|");
            File.WriteAllText(Path.Combine(testDir, "test4.doc"), "MSH|^~\\&|"); // Should be ignored

            // Act
            var files = Program.GetHl7Files(testDir);

            // Assert
            Assert.AreEqual(3, files.Count);
            Assert.IsTrue(files.Any(f => f.EndsWith("test1.hl7")));
            Assert.IsTrue(files.Any(f => f.EndsWith("test2.txt")));
            Assert.IsTrue(files.Any(f => f.EndsWith("test3.msg")));
            Assert.IsFalse(files.Any(f => f.EndsWith("test4.doc")));
        }
        finally
        {
            // Cleanup
            if (Directory.Exists(testDir))
            {
                Directory.Delete(testDir, true);
            }
        }
    }

    [TestMethod]
    public void GetHl7Files_NonExistentDirectory_ReturnsEmptyList()
    {
        // Arrange
        var nonExistentDir = Path.Combine(Path.GetTempPath(), "NonExistent_" + Guid.NewGuid().ToString("N"));

        // Act
        var files = Program.GetHl7Files(nonExistentDir);

        // Assert
        Assert.AreEqual(0, files.Count);
    }

    [TestMethod]
    public void FormatFileSize_VariousSizes_ReturnsCorrectFormat()
    {
        // Arrange & Act & Assert
        Assert.AreEqual("0 B", Program.FormatFileSize(0));
        Assert.AreEqual("512 B", Program.FormatFileSize(512));
        Assert.AreEqual("1 KB", Program.FormatFileSize(1024));
        Assert.AreEqual("1.5 KB", Program.FormatFileSize(1536));
        Assert.AreEqual("1 MB", Program.FormatFileSize(1024 * 1024));
        Assert.AreEqual("1 GB", Program.FormatFileSize(1024L * 1024 * 1024));
    }

    [TestMethod]
    public void FormatTimeSpan_VariousDurations_ReturnsCorrectFormat()
    {
        // Arrange & Act & Assert
        Assert.AreEqual("500ms", Program.FormatTimeSpan(TimeSpan.FromMilliseconds(500)));
        Assert.AreEqual("1.5s", Program.FormatTimeSpan(TimeSpan.FromSeconds(1.5)));
        Assert.AreEqual("2m 30s", Program.FormatTimeSpan(TimeSpan.FromMinutes(2.5)));
        Assert.AreEqual("1h 30m 45s", Program.FormatTimeSpan(new TimeSpan(1, 30, 45)));
    }

    [TestMethod]
    public void GetErrorCategory_VariousErrors_ReturnsCorrectCategory()
    {
        // Arrange & Act & Assert
        Assert.AreEqual("HL7 Conformance", Program.GetErrorCategory("HL7 Conformance Error: Invalid segment"));
        Assert.AreEqual("Encoding Issues", Program.GetErrorCategory("Cannot determine encoding"));
        Assert.AreEqual("Version Compliance", Program.GetErrorCategory("Version not 2.5 or above"));
        Assert.AreEqual("Empty Files", Program.GetErrorCategory("File is empty"));
        Assert.AreEqual("Parsing Errors", Program.GetErrorCategory("Failed to parse message"));
        Assert.AreEqual("Missing Segments", Program.GetErrorCategory("Missing MSH segment"));
        Assert.AreEqual("Null Message", Program.GetErrorCategory("Message is null"));
        Assert.AreEqual("Other Errors", Program.GetErrorCategory("Some other error"));
    }

    [TestMethod]
    public void ParseCommandLineArguments_NoArguments_ReturnsDefaultConfig()
    {
        // Arrange
        string[] args = [];

        // Act
        var config = Program.ParseCommandLineArguments(args);

        // Assert
        Assert.IsNull(config.MessageLimit);
    }

    [TestMethod]
    public void ParseCommandLineArguments_WithLimit_SetsLimit()
    {
        // Arrange
        string[] args = ["--limit", "50"];

        // Act
        var config = Program.ParseCommandLineArguments(args);

        // Assert
        Assert.AreEqual(50, config.MessageLimit);
    }

    [TestMethod]
    public void ParseCommandLineArguments_WithShortLimit_SetsLimit()
    {
        // Arrange
        string[] args = ["-l", "25"];

        // Act
        var config = Program.ParseCommandLineArguments(args);

        // Assert
        Assert.AreEqual(25, config.MessageLimit);
    }

    [TestMethod]
    public void ParseCommandLineArguments_InvalidLimit_UsesDefault()
    {
        // Arrange
        string[] args = ["--limit", "invalid"];

        // Act
        var config = Program.ParseCommandLineArguments(args);

        // Assert
        Assert.IsNull(config.MessageLimit);
    }
}
