﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="NHapi.Base" Version="3.2.3" />
        <PackageReference Include="NHapi.Model.V28" Version="3.2.4" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="TestValidator\**" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Remove="TestValidator\**" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="TestValidator\**" />
    </ItemGroup>

</Project>
